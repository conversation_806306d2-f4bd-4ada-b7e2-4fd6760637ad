package handlers

import (
    "fmt"
    "net/http"
    "strings"
    "vault/pgp"
    "vault/storage"
)

func DecryptHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Only GET allowed", http.StatusMethodNotAllowed)
        return
    }

    docID := strings.TrimPrefix(r.URL.Path, "/decrypt/")
    if docID == "" {
        http.Error(w, "Missing doc_id", http.StatusBadRequest)
        return
    }

    encPath, filename, err := storage.RetrieveEncryptedFile(docID)
    if err != nil {
        http.Error(w, "Document not found: "+err.<PERSON>rror(), http.StatusNotFound)
        return
    }
    defer storage.CleanupTemp(encPath)

    decPath := encPath + ".dec"
    if err := pgp.DecryptFile(encPath, decPath); err != nil {
        http.Error(w, "Decryption failed: "+err.Error(), http.StatusInternalServerError)
        return
    }
    defer storage.CleanupTemp(decPath)

    w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%q", filename))
    w.Header().Set("Content-Type", "application/pdf")
    http.ServeFile(w, r, decPath)
}
