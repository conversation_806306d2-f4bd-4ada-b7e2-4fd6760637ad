package storage

import (
    "context"
    "encoding/json"
    "time"

    "github.com/redis/go-redis/v9"
)

var rdb *redis.Client
var ctx = context.Background()

func InitRedis(addr string) error {
    rdb = redis.NewClient(&redis.Options{
        Addr: addr,
        DB:   0,
    })
    return rdb.Ping(ctx).Err()
}

func CacheMetadata(meta DocumentMetadata) {
    data, _ := json.Marshal(meta)
    rdb.Set(ctx, "doc:"+meta.ID, data, 24*time.Hour)
}
