package main

import (
    "log"
    "net/http"
    "os"
    "vault/handlers"
    "vault/storage"
)

func main() {
    pgConn := os.Getenv("POSTGRES_CONN")
    redisAddr := os.Getenv("REDIS_ADDR")
    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }

    if err := storage.InitPostgres(pgConn); err != nil {
        log.Fatal("Postgres connection error: ", err)
    }
    if err := storage.InitRedis(redisAddr); err != nil {
        log.Fatal("Redis connection error: ", err)
    }

    mux := http.NewServeMux()
    mux.HandleFunc("/upload", handlers.UploadHandler)
    mux.HandleFunc("/decrypt/", handlers.DecryptHandler)
    mux.HandleFunc("/search", handlers.SearchHandler)
    mux.HandleFunc("/health", handlers.HealthHandler)

    log.Println("Vault service running on port", port)
    if err := http.ListenAndServe(":"+port, mux); err != nil {
        log.Fatal(err)
    }
}
